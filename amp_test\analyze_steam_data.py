#!/usr/bin/env python3
"""
Steam User Data Analysis Script

This script processes Steam user data from two weekly snapshots to extract insights about:
1. New game copies added between weeks (copies_added.tsv)
2. Inactive users whose play times didn't change (inactive_users.tsv)
"""

import pandas as pd
from collections import defaultdict
import sys
import os

def load_data(filename):
    """Load TSV data and return as DataFrame"""
    try:
        df = pd.read_csv(filename, sep='\t')
        print(f"Loaded {len(df)} records from {filename}")
        return df
    except FileNotFoundError:
        print(f"Error: File {filename} not found")
        sys.exit(1)
    except Exception as e:
        print(f"Error loading {filename}: {e}")
        sys.exit(1)

def find_copies_added(week1_df, week2_df):
    """
    Find games that were added between week 1 and week 2.
    A game is considered "added" if a user has it in week 2 but not in week 1.
    """
    # Create sets of (steam_id, game_id) pairs for each week
    week1_pairs = set(zip(week1_df['steam_id'], week1_df['game_id']))
    week2_pairs = set(zip(week2_df['steam_id'], week2_df['game_id']))
    
    # Find new pairs (games added by users)
    new_pairs = week2_pairs - week1_pairs
    
    # Count how many copies of each game were added
    game_copies_added = defaultdict(int)
    for steam_id, game_id in new_pairs:
        game_copies_added[game_id] += 1
    
    # Convert to list of tuples and sort by game_id
    copies_added = [(game_id, count) for game_id, count in game_copies_added.items()]
    copies_added.sort(key=lambda x: x[0])
    
    return copies_added

def find_inactive_users(week1_df, week2_df):
    """
    Find users who were inactive between week 1 and week 2.
    A user is considered inactive if none of their game play times changed.
    """
    # Create dictionaries mapping (steam_id, game_id) to play_time for each week
    week1_playtime = {}
    week2_playtime = {}
    
    for _, row in week1_df.iterrows():
        week1_playtime[(row['steam_id'], row['game_id'])] = row['play_time']
    
    for _, row in week2_df.iterrows():
        week2_playtime[(row['steam_id'], row['game_id'])] = row['play_time']
    
    # Get all users from both weeks
    all_users_week1 = set(week1_df['steam_id'])
    all_users_week2 = set(week2_df['steam_id'])
    
    # Only consider users present in both weeks
    common_users = all_users_week1.intersection(all_users_week2)
    
    inactive_users = []
    
    for user in common_users:
        # Get all games for this user in both weeks
        user_games_week1 = set(week1_df[week1_df['steam_id'] == user]['game_id'])
        user_games_week2 = set(week2_df[week2_df['steam_id'] == user]['game_id'])
        
        # Only consider games that exist in both weeks for this user
        common_games = user_games_week1.intersection(user_games_week2)
        
        if not common_games:
            # If user has no common games, skip (they might have changed their entire library)
            continue
        
        # Check if any play time changed for common games
        play_time_changed = False
        for game_id in common_games:
            week1_time = week1_playtime.get((user, game_id), 0)
            week2_time = week2_playtime.get((user, game_id), 0)
            
            if week1_time != week2_time:
                play_time_changed = True
                break
        
        # If no play time changed for any common game, user is inactive
        if not play_time_changed:
            inactive_users.append(user)
    
    # Sort users for consistent output
    inactive_users.sort()
    
    return inactive_users

def save_copies_added(copies_added, filename):
    """Save copies added data to TSV file"""
    with open(filename, 'w') as f:
        f.write("game_id\tcopies_added\n")
        for game_id, count in copies_added:
            f.write(f"{game_id}\t{count}\n")
    print(f"Saved {len(copies_added)} games with added copies to {filename}")

def save_inactive_users(inactive_users, filename):
    """Save inactive users data to TSV file"""
    with open(filename, 'w') as f:
        f.write("steam_id\n")
        for user in inactive_users:
            f.write(f"{user}\n")
    print(f"Saved {len(inactive_users)} inactive users to {filename}")

def main():
    """Main function to orchestrate the analysis"""
    print("Steam User Data Analysis")
    print("=" * 40)
    
    # Load data
    week1_df = load_data('week_1.tsv')
    week2_df = load_data('week_2.tsv')
    
    print(f"\nWeek 1: {len(week1_df['steam_id'].unique())} unique users, {len(week1_df['game_id'].unique())} unique games")
    print(f"Week 2: {len(week2_df['steam_id'].unique())} unique users, {len(week2_df['game_id'].unique())} unique games")
    
    # Find copies added
    print("\nAnalyzing new game copies...")
    copies_added = find_copies_added(week1_df, week2_df)
    total_copies_added = sum(count for _, count in copies_added)
    print(f"Found {len(copies_added)} games with new copies, totaling {total_copies_added} new copies")
    
    # Find inactive users
    print("\nAnalyzing user activity...")
    inactive_users = find_inactive_users(week1_df, week2_df)
    print(f"Found {len(inactive_users)} inactive users")
    
    # Save results
    print("\nSaving results...")
    save_copies_added(copies_added, 'copies_added.tsv')
    save_inactive_users(inactive_users, 'inactive_users.tsv')
    
    print("\nAnalysis complete!")
    
    # Print some summary statistics
    if copies_added:
        top_games = sorted(copies_added, key=lambda x: x[1], reverse=True)[:5]
        print(f"\nTop 5 games by copies added:")
        for game_id, count in top_games:
            print(f"  Game {game_id}: {count} copies")

if __name__ == "__main__":
    main()
