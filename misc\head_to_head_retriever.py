
import requests
import lxml.html
from itertools import combinations
import time
import json
import csv

def load_team_ids(filename):
    """Load team IDs from the specified file."""
    teams = {}
    try:
        with open(filename, 'r', encoding='utf-8') as file:
            for line in file:
                line = line.strip()
                if line and ',' in line:
                    country_name, team_id = line.split(',', 1)
                    teams[country_name.strip()] = team_id.strip()
    except FileNotFoundError:
        print(f"Error: File '{filename}' not found.")
        return {}
    except Exception as e:
        print(f"Error reading file '{filename}': {e}")
        return {}

    return teams

def extract_head_to_head_record(tree):
    """Extract win/draw/loss record from the HTML tree."""
    try:
        # Look for the total row with class="total"
        total_row = tree.xpath('//tr[@class="total"]')

        if not total_row:
            # No total row found, teams haven't played each other
            return (0, 0, 0)

        # Get the first total row (should be for the first team)
        row = total_row[0]

        # Extract the td elements - should be: Total, Wins, Draws, Losses
        tds = row.xpath('.//td')

        if len(tds) >= 4:
            # Skip first td (contains "Total"), get wins, draws, losses
            wins = int(tds[1].text_content().strip())
            draws = int(tds[2].text_content().strip())
            losses = int(tds[3].text_content().strip())
            return (wins, draws, losses)
        else:
            # Unexpected format
            return (0, 0, 0)

    except (ValueError, IndexError, AttributeError) as e:
        print(f"Error parsing head-to-head record: {e}")
        return (0, 0, 0)

def extract_match_history(tree, team1_name, team2_name):
    """Extract match history and calculate average scores for each team."""
    try:
        # Look for match rows with class="match"
        match_rows = tree.xpath('//tr[@class="match"]')

        if not match_rows:
            # No match history found
            return {
                'matches_played': 0,
                'avg_team1_score': 0.0,
                'avg_team2_score': 0.0,
                'total_goals_team1': 0,
                'total_goals_team2': 0,
                'matches': []
            }

        matches = []
        total_team1_goals = 0
        total_team2_goals = 0

        for match_row in match_rows:
            try:
                # Extract home team
                home_team_elem = match_row.xpath('.//td[@class="team homeTeam"]//a')
                home_team = home_team_elem[0].text_content().strip() if home_team_elem else ""

                # Extract away team
                away_team_elem = match_row.xpath('.//td[@class="team awayTeam"]//a')
                away_team = away_team_elem[0].text_content().strip() if away_team_elem else ""

                # Extract score from <em> tags within score cell
                score_cell = match_row.xpath('.//td[@class="score"]')
                if score_cell:
                    em_tags = score_cell[0].xpath('.//em')
                    if len(em_tags) >= 2:
                        home_score = int(em_tags[0].text_content().strip())
                        away_score = int(em_tags[1].text_content().strip())

                        # Extract date
                        date_elem = match_row.xpath('.//td[@class="dateTime"]//a')
                        date = date_elem[0].get('title', '') if date_elem else ""

                        # Determine which team scored which goals
                        # We need to match team names (may need fuzzy matching)
                        team1_score = 0
                        team2_score = 0

                        # Simple name matching - check if team names are contained in the full team names
                        if team1_name.lower() in home_team.lower():
                            team1_score = home_score
                            team2_score = away_score
                        elif team1_name.lower() in away_team.lower():
                            team1_score = away_score
                            team2_score = home_score
                        elif team2_name.lower() in home_team.lower():
                            team2_score = home_score
                            team1_score = away_score
                        elif team2_name.lower() in away_team.lower():
                            team2_score = away_score
                            team1_score = home_score
                        else:
                            # Fallback: assume first team is home team for this match
                            team1_score = home_score
                            team2_score = away_score

                        match_info = {
                            'date': date,
                            'home_team': home_team,
                            'away_team': away_team,
                            'home_score': home_score,
                            'away_score': away_score,
                            'team1_score': team1_score,
                            'team2_score': team2_score
                        }
                        matches.append(match_info)

                        total_team1_goals += team1_score
                        total_team2_goals += team2_score

            except (ValueError, IndexError, AttributeError) as e:
                print(f"Error parsing individual match: {e}")
                continue

        # Calculate averages
        num_matches = len(matches)
        avg_team1_score = total_team1_goals / num_matches if num_matches > 0 else 0.0
        avg_team2_score = total_team2_goals / num_matches if num_matches > 0 else 0.0

        return {
            'matches_played': num_matches,
            'avg_team1_score': round(avg_team1_score, 2),
            'avg_team2_score': round(avg_team2_score, 2),
            'total_goals_team1': total_team1_goals,
            'total_goals_team2': total_team2_goals,
            'matches': matches
        }

    except Exception as e:
        print(f"Error extracting match history: {e}")
        return {
            'matches_played': 0,
            'avg_team1_score': 0.0,
            'avg_team2_score': 0.0,
            'total_goals_team1': 0,
            'total_goals_team2': 0,
            'matches': []
        }

def scrape_head_to_head(team_id1, team_id2, team1_name, team2_name):
    """Scrape head-to-head data for two teams."""
    url = f"https://www.soccerbase.com/teams/team.sd?team_id={team_id1}&team2_id={team_id2}&teamTabs=h2h"

    try:
        print(f"\nScraping: {team1_name} vs {team2_name}")

        # Add headers to mimic a real browser
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        }

        response = requests.get(url, headers=headers, timeout=10)
        response.raise_for_status()

        # Parse with lxml
        tree = lxml.html.fromstring(response.content)

        # Extract head-to-head record
        wins, draws, losses = extract_head_to_head_record(tree)

        # Extract match history and calculate averages
        match_history = extract_match_history(tree, team1_name, team2_name)

        return {
            'team1': team1_name,
            'team2': team2_name,
            'wins': wins,
            'draws': draws,
            'losses': losses,
            'record': f"{wins}-{draws}-{losses}",
            'matches_played': match_history['matches_played'],
            'avg_team1_score': match_history['avg_team1_score'],
            'avg_team2_score': match_history['avg_team2_score'],
            'total_goals_team1': match_history['total_goals_team1'],
            'total_goals_team2': match_history['total_goals_team2'],
            'matches': match_history['matches']
        }

    except requests.exceptions.RequestException as e:
        print(f"Error fetching {url}: {e}")
        return None
    except Exception as e:
        print(f"Error processing {team1_name} vs {team2_name}: {e}")
        return None

def save_results_to_csv(results, filename='head_to_head_results.csv'):
    """Save results to a CSV file."""
    try:
        with open(filename, 'w', newline='', encoding='utf-8') as csvfile:
            fieldnames = ['team1', 'team2', 'wins', 'draws', 'losses', 'record',
                         'matches_played', 'avg_team1_score', 'avg_team2_score',
                         'total_goals_team1', 'total_goals_team2']
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)

            writer.writeheader()
            for result in results:
                if result:  # Only write successful results
                    # Create a copy without the 'matches' field for CSV
                    csv_result = {k: v for k, v in result.items() if k != 'matches'}
                    writer.writerow(csv_result)

        print(f"\nResults saved to {filename}")
        print(f"Total records saved: {len([r for r in results if r])}")

    except Exception as e:
        print(f"Error saving to CSV: {e}")

def save_results_to_json(results, filename='head_to_head_results.json'):
    """Save results to a JSON file."""
    try:
        # Filter out None results
        valid_results = [result for result in results if result]

        with open(filename, 'w', encoding='utf-8') as jsonfile:
            json.dump(valid_results, jsonfile, indent=2, ensure_ascii=False)

        print(f"Results also saved to {filename}")

    except Exception as e:
        print(f"Error saving to JSON: {e}")

def main():
    """Main function to scrape head-to-head data for all team combinations."""
    # Load team IDs
    teams = load_team_ids('h2h_ids.txt')

    if not teams:
        print("No teams loaded. Exiting.")
        return

    print(f"Loaded {len(teams)} teams:")
    for country, team_id in teams.items():
        print(f"  {country}: {team_id}")

    # Generate all combinations of teams (each pair only once)
    team_pairs = list(combinations(teams.items(), 2))
    print(f"\nWill scrape {len(team_pairs)} head-to-head matchups")

    all_results = []

    # Scrape each combination
    for i, ((team1_name, team1_id), (team2_name, team2_id)) in enumerate(team_pairs, 1):
        print(f"\n{'='*60}")
        print(f"Processing {i}/{len(team_pairs)}")

        result = scrape_head_to_head(team1_id, team2_id, team1_name, team2_name)
        all_results.append(result)

        if result:
            print("✓ Successfully scraped")
        else:
            print("✗ Failed to scrape")

        # Add delay to be respectful to the server
        if i < len(team_pairs):
            print("Waiting 1 seconds before next request...")
            time.sleep(1)

    # Save results to files
    print(f"\n{'='*60}")
    print("Saving results to files...")
    save_results_to_csv(all_results)
    save_results_to_json(all_results)

    # Print summary
    successful_results = [r for r in all_results if r]
    failed_results = len(all_results) - len(successful_results)

    print(f"\nScraping Summary:")
    print(f"  Total matchups processed: {len(all_results)}")
    print(f"  Successful: {len(successful_results)}")
    print(f"  Failed: {failed_results}")

    if successful_results:
        print(f"\nSample results:")
        for result in successful_results[:3]:  # Show first 3 results
            print(f"  {result['team1']} vs {result['team2']}: {result['record']}")
        if len(successful_results) > 3:
            print(f"  ... and {len(successful_results) - 3} more")

if __name__ == "__main__":
    main()